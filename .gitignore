# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependency directories
node_modules/
jspm_packages/

# Build outputs
dist/
dist-ssr/
build/
out/
.output/
.nuxt/
.next/
.cache/
.parcel-cache/
.vite/

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.local

# Docker volumes
data/
postgres-data/
typesense-data/

# Testing
coverage/
.nyc_output/
cypress/screenshots/
cypress/videos/

# Playwright (generated test artifacts only)
playwright-report/
test-results/
playwright/.cache/
# Jest
.jest-cache/
jest-coverage/
# Other test frameworks
reports/
test-report/
allure-results/
allure-report/

# Temporary files
tmp/
temp/
.tmp/
.temp/
*.tmp
*.temp
*.bak
*.swp
*.swo

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
.idea/
*.sublime-project
*.sublime-workspace
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
Icon?
ehthumbs.db
Thumbs.db

# Debug files
*.stackdump
*.dump
*.core
*.pid
*.seed
*.pid.lock

# Package files
*.tgz
*.tar.gz
*.zip
*.rar

# Compiled binary addons
build/Release/
*.node

# TypeScript
*.tsbuildinfo

# Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.pnp.*



# PostgreSQL dumps
*.dump
*.sql.gz
*.pgsql

# CSV test files
csv/

# Production build artifacts
dist/
build/
*.tsbuildinfo

# Generated documentation
docs/generated/

# Miscellaneous
.vercel
.netlify
