# Docker Compose Override for Development
# Use: docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d

services:
  # Expose database port for local development
  postgres:
    ports:
      - "5432:5432"

  # Expose MinIO ports for local development
  minio:
    ports:
      - "9000:9000"
      - "9001:9001"

  # Expose Typesense port for local development
  typesense:
    ports:
      - "8108:8108"

  # Expose Redis port for local development
  redis:
    ports:
      - "6379:6379"

  # Expose pgAdmin for local development
  pgadmin:
    ports:
      - "5050:80"
