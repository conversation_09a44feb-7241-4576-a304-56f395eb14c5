# Docker Compose for Development with Vite
# Use: docker compose -f docker-compose.yml -f docker-compose.vite.yml up -d

services:
  # Frontend with Vite development server
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "5173:5173"  # Vite default port
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3001/api
    command: npm run dev
    depends_on:
      - api

  # Expose database port for local development
  postgres:
    ports:
      - "5432:5432"

  # Expose MinIO ports for local development
  minio:
    ports:
      - "9000:9000"
      - "9001:9001"

  # Expose Typesense port for local development
  typesense:
    ports:
      - "8108:8108"

  # Expose Redis port for local development
  redis:
    ports:
      - "6379:6379"

  # Expose pgAdmin for local development
  pgadmin:
    ports:
      - "5050:80"
