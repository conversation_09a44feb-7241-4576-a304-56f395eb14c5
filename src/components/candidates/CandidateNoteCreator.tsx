/**
 * Component for creating candidate notes
 * This is the note creation interface that appears below Quick Actions
 */

import React, { useState } from 'react';
import { MessageSquare } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { candidateNotesApi } from '@/services/candidateNotesApi';
import { useUser } from '@/contexts/UserContext';

interface CandidateNoteCreatorProps {
  candidateId: string;
  onNoteAdded?: (noteId: string) => void;
  notesCount?: number;
}

export function CandidateNoteCreator({ candidateId, onNoteAdded, notesCount = 0 }: CandidateNoteCreatorProps) {
  const [newNote, setNewNote] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { user } = useUser();

  const handleAddNote = async () => {
    console.log('📝 CandidateNoteCreator: Adding note for candidate:', candidateId);
    console.log('👤 CandidateNoteCreator: Current user:', user);
    console.log('📄 CandidateNoteCreator: Note content:', newNote.trim());
    
    if (!newNote.trim()) {
      console.log('⚠️ CandidateNoteCreator: Empty note content');
      toast({
        title: 'Error',
        description: 'Please enter a note before submitting',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);
      console.log('🚀 CandidateNoteCreator: Calling API to create note...');
      
      const noteData = {
        candidate_id: candidateId,
        content: newNote.trim(),
        user_id: user.id,
      };
      console.log('📤 CandidateNoteCreator: Note data being sent:', noteData);
      
      const createdNote = await candidateNotesApi.create(noteData);
      console.log('✅ CandidateNoteCreator: Note created successfully:', createdNote);

      setNewNote('');

      // Call the callback with the new note ID
      onNoteAdded?.(createdNote.id);

      toast({
        title: 'Success',
        description: 'Note added successfully',
      });
    } catch (error) {
      console.error('❌ CandidateNoteCreator: Error adding note:', error);
      console.error('❌ CandidateNoteCreator: Error details:', error.response?.data || error.message);
      toast({
        title: 'Error',
        description: 'Failed to add note',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Add Note
          {notesCount > 0 && (
            <Badge variant="secondary" className="ml-auto">
              {notesCount} {notesCount === 1 ? 'note' : 'notes'}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <Textarea
          placeholder="Add a note about this candidate..."
          value={newNote}
          onChange={(e) => setNewNote(e.target.value)}
          className="min-h-[100px]"
        />
        <Button 
          onClick={handleAddNote} 
          disabled={isSubmitting || !newNote.trim()}
          type="button"
          className="w-full"
        >
          {isSubmitting ? 'Adding Note...' : 'Add Note'}
        </Button>
      </CardContent>
    </Card>
  );
}
