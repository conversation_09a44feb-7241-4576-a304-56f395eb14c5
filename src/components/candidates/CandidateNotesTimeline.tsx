/**
 * Timeline component for displaying candidate notes
 */

import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { User, MessageSquare, Clock, Edit2, Trash2 } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { candidateNotesApi } from '@/services/candidateNotesApi';
import { CandidateNote } from '@/types/candidateNote';
import { useUser } from '@/contexts/UserContext';

interface CandidateNotesTimelineProps {
  candidateId: string;
  onNoteAdded?: () => void;
  showCreateInterface?: boolean;
  onNotesCountChange?: (count: number) => void;
  refreshTrigger?: number;
}

export function CandidateNotesTimeline({ candidateId, onNoteAdded, showCreateInterface = true, onNotesCountChange, refreshTrigger }: CandidateNotesTimelineProps) {
  const [notes, setNotes] = useState<CandidateNote[]>([]);
  const [newNote, setNewNote] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingNoteId, setEditingNoteId] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');
  const [lastAddedNoteId, setLastAddedNoteId] = useState<string | null>(null);
  const { toast } = useToast();
  const { user } = useUser();
  const newNoteRef = React.useRef<HTMLDivElement>(null);

  // Debug: Log user and candidateId changes
  useEffect(() => {
    console.log('👤 CandidateNotesTimeline: User context changed:', user);
  }, [user]);

  useEffect(() => {
    console.log('🆔 CandidateNotesTimeline: Candidate ID changed:', candidateId, 'Type:', typeof candidateId);
  }, [candidateId]);

  // Load notes when component mounts or when refresh is triggered
  useEffect(() => {
    console.log('🔄 CandidateNotesTimeline: Component mounted, candidateId:', candidateId);
    loadNotes();
  }, [candidateId]);

  // Refresh notes when refreshTrigger changes
  useEffect(() => {
    if (refreshTrigger !== undefined) {
      console.log('🔄 CandidateNotesTimeline: Refresh triggered:', refreshTrigger);
      loadNotes();
    }
  }, [refreshTrigger]);

  const loadNotes = async () => {
    console.log('📥 CandidateNotesTimeline: Loading notes for candidate:', candidateId);
    try {
      setIsLoading(true);
      const fetchedNotes = await candidateNotesApi.getByCandidateId(candidateId);
      console.log('✅ CandidateNotesTimeline: Fetched notes:', fetchedNotes);
      console.log('📊 CandidateNotesTimeline: Number of notes fetched:', fetchedNotes.length);
      setNotes(fetchedNotes);
      onNotesCountChange?.(fetchedNotes.length);
    } catch (error) {
      console.error('❌ CandidateNotesTimeline: Error loading notes:', error);
      toast({
        title: 'Error',
        description: 'Failed to load candidate notes',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddNote = async () => {
    console.log('📝 CandidateNotesTimeline: Adding note for candidate:', candidateId);
    console.log('👤 CandidateNotesTimeline: Current user:', user);
    console.log('📄 CandidateNotesTimeline: Note content:', newNote.trim());

    if (!newNote.trim()) {
      console.log('⚠️ CandidateNotesTimeline: Empty note content');
      toast({
        title: 'Error',
        description: 'Please enter a note before submitting',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);
      console.log('🚀 CandidateNotesTimeline: Calling API to create note...');

      const noteData = {
        candidate_id: candidateId,
        content: newNote.trim(),
        user_id: user.id,
      };
      console.log('📤 CandidateNotesTimeline: Note data being sent:', noteData);

      const createdNote = await candidateNotesApi.create(noteData);
      console.log('✅ CandidateNotesTimeline: Note created successfully:', createdNote);

      // Add the new note to the beginning of the list
      console.log('🔄 CandidateNotesTimeline: Current notes before adding:', notes);
      setNotes(prev => {
        const newNotes = [createdNote, ...prev];
        console.log('🔄 CandidateNotesTimeline: New notes array:', newNotes);
        return newNotes;
      });
      setNewNote('');
      setLastAddedNoteId(createdNote.id);

      // Call the callback to switch tabs if needed
      onNoteAdded?.();

      // Auto-scroll to the new note after a short delay to allow for tab switching
      setTimeout(() => {
        if (newNoteRef.current) {
          newNoteRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      }, 100);

      toast({
        title: 'Success',
        description: 'Note added successfully',
      });
    } catch (error) {
      console.error('❌ CandidateNotesTimeline: Error adding note:', error);
      console.error('❌ CandidateNotesTimeline: Error details:', error.response?.data || error.message);
      toast({
        title: 'Error',
        description: 'Failed to add note',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditNote = (note: CandidateNote) => {
    setEditingNoteId(note.id);
    setEditContent(note.content);
  };

  const handleSaveEdit = async (noteId: string) => {
    if (!editContent.trim()) {
      toast({
        title: 'Error',
        description: 'Note content cannot be empty',
        variant: 'destructive',
      });
      return;
    }

    try {
      const updatedNote = await candidateNotesApi.update(noteId, {
        content: editContent.trim(),
        user_id: user.id,
      });

      // Update the note in the list
      setNotes(prev => prev.map(note => 
        note.id === noteId ? updatedNote : note
      ));

      setEditingNoteId(null);
      setEditContent('');

      toast({
        title: 'Success',
        description: 'Note updated successfully',
      });
    } catch (error) {
      console.error('Error updating note:', error);
      toast({
        title: 'Error',
        description: 'Failed to update note',
        variant: 'destructive',
      });
    }
  };

  const handleDeleteNote = async (noteId: string) => {
    if (!confirm('Are you sure you want to delete this note?')) {
      return;
    }

    try {
      await candidateNotesApi.delete(noteId, user.id);

      // Remove the note from the list
      setNotes(prev => prev.filter(note => note.id !== noteId));

      toast({
        title: 'Success',
        description: 'Note deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting note:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete note',
        variant: 'destructive',
      });
    }
  };

  const cancelEdit = () => {
    setEditingNoteId(null);
    setEditContent('');
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy \'at\' h:mm a');
    } catch (error) {
      return 'Invalid date';
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Notes Timeline
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">Loading notes...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card data-timeline-container>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Notes Timeline
          <Badge variant="secondary" className="ml-auto">
            {notes.length} {notes.length === 1 ? 'note' : 'notes'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Add new note section - only show if showCreateInterface is true */}
        {showCreateInterface && (
          <div className="space-y-3">
            <Textarea
              placeholder="Add a note about this candidate..."
              value={newNote}
              onChange={(e) => setNewNote(e.target.value)}
              className="min-h-[100px]"
            />
            <Button
              onClick={handleAddNote}
              disabled={isSubmitting || !newNote.trim()}
              type="button"
            >
              {isSubmitting ? 'Adding Note...' : 'Add Note'}
            </Button>
          </div>
        )}

        {/* Notes timeline */}
        {notes.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No notes yet. Add the first note above!</p>
          </div>
        ) : (
          <div className="space-y-4">
            {notes.map((note, index) => (
              <div
                key={note.id}
                className="relative"
                ref={note.id === lastAddedNoteId ? newNoteRef : null}
              >
                {/* Timeline line */}
                {index < notes.length - 1 && (
                  <div className="absolute left-6 top-12 w-0.5 h-full bg-border" />
                )}
                
                <div className="flex gap-4">
                  {/* Avatar */}
                  <Avatar className="h-12 w-12 border-2 border-background">
                    <AvatarImage src={note.user_avatar} />
                    <AvatarFallback>
                      {note.user_name ? getInitials(note.user_name) : <User className="h-6 w-6" />}
                    </AvatarFallback>
                  </Avatar>

                  {/* Note content */}
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{note.user_name || 'Unknown User'}</p>
                        <p className="text-sm text-muted-foreground flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {formatDate(note.created_at)}
                          {note.updated_at !== note.created_at && (
                            <span className="text-xs">(edited)</span>
                          )}
                        </p>
                      </div>
                      
                      {/* Edit/Delete buttons for own notes */}
                      {note.user_id === user.id && (
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditNote(note)}
                            className="h-8 w-8 p-0"
                          >
                            <Edit2 className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteNote(note.id)}
                            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                    </div>

                    {/* Note content - editable or display */}
                    {editingNoteId === note.id ? (
                      <div className="space-y-2">
                        <Textarea
                          value={editContent}
                          onChange={(e) => setEditContent(e.target.value)}
                          className="min-h-[80px]"
                        />
                        <div className="flex gap-2">
                          <Button size="sm" onClick={() => handleSaveEdit(note.id)}>
                            Save
                          </Button>
                          <Button size="sm" variant="outline" onClick={cancelEdit}>
                            Cancel
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-muted/50 rounded-lg p-3">
                        <p className="text-sm whitespace-pre-wrap">{note.content}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
