import { useState, useCallback, useMemo, useEffect } from 'react';
import { AgGridReact } from 'ag-grid-react';
import {
  ColDef,
  GridReadyEvent,
  ModuleRegistry,
  GridApi,
  SelectionChangedEvent
} from 'ag-grid-community';
import { AllCommunityModule } from 'ag-grid-community';
import { Candidate, CandidateStatus } from '@/types/candidate';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useNavigate } from 'react-router-dom';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { getCurrentGridTheme, setupGridThemeListener } from '@/lib/grid/grid-theme';
import { Button } from '@/components/ui/button';
import { Trash2, MoreHorizontal, UserMinus, Eye } from 'lucide-react';
import DeleteConfirmationDialog from '@/components/common/DeleteConfirmationDialog';
import { candidates<PERSON><PERSON> } from '@/services/apiService';
import { useToast } from '@/components/ui/use-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ColumnVisibilityProvider, useColumnVisibility } from '@/contexts/ColumnVisibilityContext';
import { ColumnVisibilityPanel } from '@/components/grid/ColumnVisibilityPanel';
import { ColumnVisibilityTrigger } from '@/components/grid/ColumnVisibilityTrigger';

// Register AG Grid Modules
ModuleRegistry.registerModules([AllCommunityModule]);

interface TableViewProps {
  candidates: Candidate[];
  viewMode?: string;
  onViewModeChange?: (mode: string) => void;
  onCandidatesChange?: () => void;
}

// Custom cell renderer for the name column with avatar
const NameCellRenderer = (props: any) => {
  const { value, data } = props;
  const name = value || '';

  return (
    <div className="flex items-center gap-2">
      <div className="relative">
        <Avatar className={`h-8 w-8 ${data.isInIncubator ? 'ring-2 ring-amber-400 ring-offset-1' : ''}`}>
          <AvatarFallback>{name.charAt(0)}</AvatarFallback>
        </Avatar>
        {data.isInIncubator && (
          <div className="absolute -bottom-0.5 -right-0.5 h-4 w-4 bg-amber-400 rounded-full flex items-center justify-center border border-white">
            <span className="text-xs">🌱</span>
          </div>
        )}
      </div>
      <span>{name}</span>
    </div>
  );
};

// Custom cell renderer for date fields
const DateCellRenderer = (props: any) => {
  const { value } = props;

  if (!value || value === 'N/A' || value === 'Not specified') {
    return <span className="text-muted-foreground">N/A</span>;
  }

  try {
    const date = new Date(value);
    if (isNaN(date.getTime())) {
      return <span className="text-muted-foreground">N/A</span>;
    }
    return <span>{date.toLocaleDateString()}</span>;
  } catch (error) {
    return <span className="text-muted-foreground">N/A</span>;
  }
};

// Custom cell renderer for URL fields
const UrlCellRenderer = (props: any) => {
  const { value } = props;

  if (!value || value === 'N/A' || value === 'Not specified') {
    return <span className="text-muted-foreground">N/A</span>;
  }

  const displayUrl = value.length > 30 ? `${value.substring(0, 30)}...` : value;

  return (
    <a
      href={value.startsWith('http') ? value : `https://${value}`}
      target="_blank"
      rel="noopener noreferrer"
      className="text-blue-600 hover:text-blue-800 hover:underline"
      title={value}
    >
      {displayUrl}
    </a>
  );
};

// Custom cell renderer for the primary status column with badge
const StatusCellRenderer = (props: any) => {
  const { data } = props;
  const status = data?.status || 'new';

  // Get status color based on primary status
  const getStatusColor = (status: CandidateStatus) => {
    switch(status) {
      case 'new': return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
      case 'screening': return 'bg-purple-100 text-purple-800 hover:bg-purple-100';
      case 'interview': return 'bg-amber-100 text-amber-800 hover:bg-amber-100';
      case 'challenge': return 'bg-orange-100 text-orange-800 hover:bg-orange-100';
      case 'client_interview': return 'bg-indigo-100 text-indigo-800 hover:bg-indigo-100';
      case 'client_feedback': return 'bg-cyan-100 text-cyan-800 hover:bg-cyan-100';
      case 'offer': return 'bg-green-100 text-green-800 hover:bg-green-100';
      case 'hired': return 'bg-emerald-100 text-emerald-800 hover:bg-emerald-100';
      case 'rejected': return 'bg-red-100 text-red-800 hover:bg-red-100';
      default: return '';
    }
  };

  return (
    <Badge variant="outline" className={getStatusColor(status as CandidateStatus)}>
      {status}
    </Badge>
  );
};

// Custom cell renderer for the secondary status column
const SecondaryStatusCellRenderer = (props: any) => {
  const { data } = props;
  const secondaryStatus = data?.secondaryStatus;

  if (!secondaryStatus) {
    return <span className="text-muted-foreground italic">-</span>;
  }

  return (
    <div className="text-sm text-foreground truncate" title={secondaryStatus}>
      {secondaryStatus}
    </div>
  );
};

// Custom cell renderer for array fields (like skills)
const ArrayCellRenderer = (props: any) => {
  const { value } = props;

  if (!value || !Array.isArray(value) || value.length === 0) {
    return <span className="text-muted-foreground italic">-</span>;
  }

  const displayValue = value.join(', ');
  const truncatedValue = displayValue.length > 50 ? `${displayValue.substring(0, 50)}...` : displayValue;

  return (
    <div className="text-sm text-foreground truncate" title={displayValue}>
      {truncatedValue}
    </div>
  );
};

// Custom cell renderer for the actions column
const ActionsCellRenderer = (props: any) => {
  const { data, context } = props;

  const handleRemoveFromIncubator = () => {
    if (context && context.onRemoveFromIncubator) {
      context.onRemoveFromIncubator(data.id);
    }
  };

  const handleViewDetails = () => {
    // Navigate to candidate details page using React Router
    if (context && context.onNavigate) {
      context.onNavigate(`/candidates/${data.id}`);
    }
  };

  return (
    <div className="flex justify-end items-center">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={handleViewDetails}>
            <Eye className="h-4 w-4 mr-2" />
            View Details
          </DropdownMenuItem>
          {data.isInIncubator && (
            <DropdownMenuItem onClick={handleRemoveFromIncubator} className="text-red-600">
              <UserMinus className="h-4 w-4 mr-2" />
              Remove from Incubator
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

// Internal component that uses the column visibility context
function TableViewInternal({ candidates, onCandidatesChange }: TableViewProps) {
  // State to store the grid API reference
  const [gridApi, setGridApi] = useState<GridApi | null>(null);
  // State for selected rows
  const [selectedRows, setSelectedRows] = useState<Candidate[]>([]);
  // State for delete confirmation dialog
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  // State for incubator removal confirmation
  const [showIncubatorRemovalDialog, setShowIncubatorRemovalDialog] = useState(false);
  const [candidateToRemove, setCandidateToRemove] = useState<string | null>(null);
  // State for column visibility panel
  const [showColumnPanel, setShowColumnPanel] = useState(false);
  // Toast for notifications
  const { toast } = useToast();
  // Navigation hook
  const navigate = useNavigate();
  // Column visibility context
  const { columnVisibility } = useColumnVisibility();

  // Apply the current theme to the grid
  const applyTheme = useCallback(() => {
    if (gridApi) {
      const theme = getCurrentGridTheme();
      gridApi.setGridOption('theme', theme);
    }
  }, [gridApi]);

  // Set up theme change listener
  useEffect(() => {
    // Set up a listener for theme changes
    const cleanup = setupGridThemeListener(() => {
      applyTheme();
    });

    // Clean up the listener when the component unmounts
    return cleanup;
  }, [applyTheme]);

  // Handle selection changes
  const onSelectionChanged = useCallback((event: SelectionChangedEvent) => {
    const selectedNodes = event.api.getSelectedNodes();
    const selectedData = selectedNodes.map(node => node.data);
    setSelectedRows(selectedData);
  }, []);

  // Handle bulk delete
  const handleBulkDelete = useCallback(async () => {
    if (selectedRows.length === 0) return;

    try {
      const selectedIds = selectedRows.map(row => row.id);
      console.log('Deleting candidates with IDs:', selectedIds);

      // Call the API to delete the candidates
      const result = await candidatesApi.bulkDelete(selectedIds);

      // Show success message
      toast({
        title: "Success",
        description: `Successfully deleted ${result.deletedCount} candidate(s).`,
      });

      // Remove the selected rows from the local state
      if (gridApi) {
        // Deselect all rows
        gridApi.deselectAll();

        // Notify parent component about the change
        if (onCandidatesChange) {
          onCandidatesChange();
        }

        console.log('Rows deleted successfully');
      }

      setShowDeleteDialog(false);
    } catch (error) {
      console.error('Error deleting candidates:', error);

      // Show error message
      toast({
        title: "Error",
        description: "Failed to delete candidates. Please try again.",
        variant: "destructive",
      });
    }
  }, [selectedRows, gridApi, onCandidatesChange, toast]);

  // Handle remove from incubator
  const handleRemoveFromIncubator = useCallback((candidateId: string) => {
    setCandidateToRemove(candidateId);
    setShowIncubatorRemovalDialog(true);
  }, []);

  // Confirm remove from incubator
  const confirmRemoveFromIncubator = useCallback(async () => {
    if (!candidateToRemove) return;

    try {
      await candidatesApi.update(candidateToRemove, {
        is_in_incubator: false
      });

      // Refresh the candidates list
      if (onCandidatesChange) {
        onCandidatesChange();
      }

      toast({
        title: "Removed from Incubator",
        description: "Candidate has been successfully removed from the Talent Incubator program",
      });
    } catch (error) {
      console.error('Error removing candidate from incubator:', error);
      toast({
        title: "Error",
        description: "Failed to remove candidate from Talent Incubator",
        variant: "destructive",
      });
    }

    setShowIncubatorRemovalDialog(false);
    setCandidateToRemove(null);
  }, [candidateToRemove, onCandidatesChange, toast]);

  // Column definitions with visibility state
  const columnDefs = useMemo<ColDef[]>(() => [
    {
      width: 50,
      pinned: 'left',
      lockPosition: true,
      sortable: false,
      filter: false,
      suppressHeaderMenuButton: true,
      headerName: '',
      suppressColumnsToolPanel: true, // Hide from columns tool panel
      // This column will contain the selection checkboxes
    },
    {
      field: 'name',
      headerName: 'Name',
      cellRenderer: NameCellRenderer,
      flex: 2,
      minWidth: 200,
      sortable: true,
      filter: true,
      pinned: 'left',
      lockPosition: true,
      hide: columnVisibility.name === false
    },
    {
      field: 'email',
      headerName: 'Email',
      flex: 2,
      minWidth: 200,
      sortable: true,
      filter: true,
      hide: columnVisibility.email === false
    },
    {
      field: 'phone',
      headerName: 'Phone',
      flex: 1.5,
      minWidth: 130,
      sortable: true,
      filter: true,
      hide: columnVisibility.phone === false
    },
    {
      field: 'position',
      headerName: 'Position',
      flex: 2,
      minWidth: 150,
      sortable: true,
      filter: true,
      hide: columnVisibility.position === false
    },
    {
      field: 'location',
      headerName: 'Location',
      flex: 1.5,
      minWidth: 120,
      sortable: true,
      filter: true,
      hide: columnVisibility.location === false
    },
    {
      field: 'experienceYears',
      headerName: 'Experience (Years)',
      flex: 1,
      minWidth: 100,
      sortable: true,
      filter: true,
      hide: columnVisibility.experienceYears === false
    },
    {
      field: 'currentCompany',
      headerName: 'Current Company',
      flex: 1.8,
      minWidth: 150,
      sortable: true,
      filter: true,
      hide: columnVisibility.currentCompany === false
    },
    {
      field: 'currentPosition',
      headerName: 'Current Position',
      flex: 1.8,
      minWidth: 150,
      sortable: true,
      filter: true,
      hide: columnVisibility.currentPosition === false
    },
    {
      field: 'education',
      headerName: 'Education',
      flex: 1.5,
      minWidth: 120,
      sortable: true,
      filter: true,
      hide: columnVisibility.education === false
    },
    {
      field: 'skills',
      headerName: 'Skills',
      cellRenderer: ArrayCellRenderer,
      flex: 2,
      minWidth: 150,
      sortable: true,
      filter: true,
      hide: columnVisibility.skills === false
    },
    {
      field: 'resumeUrl',
      headerName: 'Resume URL',
      cellRenderer: UrlCellRenderer,
      flex: 1.5,
      minWidth: 120,
      sortable: true,
      filter: true,
      hide: columnVisibility.resumeUrl === false
    },
    {
      field: 'linkedinUrl',
      headerName: 'LinkedIn URL',
      cellRenderer: UrlCellRenderer,
      flex: 1.5,
      minWidth: 120,
      sortable: true,
      filter: true,
      hide: columnVisibility.linkedinUrl === false
    },
    {
      field: 'githubUrl',
      headerName: 'GitHub URL',
      cellRenderer: UrlCellRenderer,
      flex: 1.5,
      minWidth: 120,
      sortable: true,
      filter: true,
      hide: columnVisibility.githubUrl === false
    },
    {
      field: 'portfolioUrl',
      headerName: 'Portfolio URL',
      cellRenderer: UrlCellRenderer,
      flex: 1.5,
      minWidth: 120,
      sortable: true,
      filter: true,
      hide: columnVisibility.portfolioUrl === false
    },
    {
      field: 'twitterUrl',
      headerName: 'Twitter URL',
      cellRenderer: UrlCellRenderer,
      flex: 1.5,
      minWidth: 120,
      sortable: true,
      filter: true,
      hide: columnVisibility.twitterUrl === false
    },
    {
      field: 'desiredSalary',
      headerName: 'Desired Salary',
      flex: 1.2,
      minWidth: 110,
      sortable: true,
      filter: true,
      hide: columnVisibility.desiredSalary === false
    },
    {
      field: 'salaryCurrency',
      headerName: 'Currency',
      flex: 0.8,
      minWidth: 80,
      sortable: true,
      filter: true,
      hide: columnVisibility.salaryCurrency === false
    },
    {
      field: 'availabilityDate',
      headerName: 'Availability Date',
      cellRenderer: DateCellRenderer,
      flex: 1.2,
      minWidth: 120,
      sortable: true,
      filter: true,
      hide: columnVisibility.availabilityDate === false
    },
    {
      field: 'appliedDate',
      headerName: 'Applied Date',
      cellRenderer: DateCellRenderer,
      flex: 1,
      minWidth: 120,
      sortable: true,
      filter: true,
      hide: columnVisibility.appliedDate === false
    },
    {
      field: 'status',
      headerName: 'Primary Status',
      cellRenderer: StatusCellRenderer,
      flex: 1.2,
      minWidth: 140,
      sortable: true,
      filter: true,
      hide: columnVisibility.status === false
    },
    {
      field: 'secondaryStatus',
      headerName: 'Secondary Status',
      cellRenderer: SecondaryStatusCellRenderer,
      flex: 1.3,
      minWidth: 150,
      sortable: true,
      filter: true,
      hide: columnVisibility.secondaryStatus === false
    },
    {
      field: 'source',
      headerName: 'Source',
      flex: 1,
      minWidth: 100,
      sortable: true,
      filter: true,
      hide: columnVisibility.source === false
    },
    {
      field: 'stargetyId',
      headerName: 'Stargety ID',
      flex: 1,
      minWidth: 100,
      sortable: true,
      filter: true,
      hide: columnVisibility.stargetyId === false
    },
    {
      field: 'isDuplicate',
      headerName: 'Duplicate Status',
      flex: 1.2,
      minWidth: 110,
      sortable: true,
      filter: true,
      hide: columnVisibility.isDuplicate === false
    },
    {
      field: 'englishLevel',
      headerName: 'English Level',
      flex: 1,
      minWidth: 100,
      sortable: true,
      filter: true,
      hide: columnVisibility.englishLevel === false
    },
    {
      field: 'interviewScore',
      headerName: 'Interview Score',
      flex: 1,
      minWidth: 110,
      sortable: true,
      filter: true,
      hide: columnVisibility.interviewScore === false
    },
    {
      field: 'challenge',
      headerName: 'Challenge',
      flex: 1.5,
      minWidth: 120,
      sortable: true,
      filter: true,
      hide: columnVisibility.challenge === false
    },
    {
      field: 'driveScore',
      headerName: 'Drive Score',
      flex: 1,
      minWidth: 100,
      sortable: true,
      filter: true,
      hide: columnVisibility.driveScore === false
    },
    {
      field: 'resilienceScore',
      headerName: 'Resilience Score',
      flex: 1.2,
      minWidth: 110,
      sortable: true,
      filter: true,
      hide: columnVisibility.resilienceScore === false
    },
    {
      field: 'collaborationScore',
      headerName: 'Collaboration Score',
      flex: 1.3,
      minWidth: 120,
      sortable: true,
      filter: true,
      hide: columnVisibility.collaborationScore === false
    },
    {
      field: 'result',
      headerName: 'Result',
      flex: 1.5,
      minWidth: 120,
      sortable: true,
      filter: true,
      hide: columnVisibility.result === false
    },
    {
      field: 'isInIncubator',
      headerName: 'In Incubator',
      flex: 1,
      minWidth: 100,
      sortable: true,
      filter: true,
      hide: columnVisibility.isInIncubator === false
    },
    {
      field: 'createdAt',
      headerName: 'Created Date',
      cellRenderer: DateCellRenderer,
      flex: 1.2,
      minWidth: 120,
      sortable: true,
      filter: true,
      hide: columnVisibility.createdAt === false
    },
    {
      field: 'updatedAt',
      headerName: 'Updated Date',
      cellRenderer: DateCellRenderer,
      flex: 1.2,
      minWidth: 120,
      sortable: true,
      filter: true,
      hide: columnVisibility.updatedAt === false
    },
    {
      headerName: 'Actions',
      cellRenderer: ActionsCellRenderer,
      flex: 1,
      minWidth: 100,
      sortable: false,
      filter: false,
      suppressColumnsToolPanel: true, // Hide from columns tool panel
      pinned: 'right'
    }
  ], [columnVisibility]);

  // Default column definitions
  const defaultColDef = useMemo(() => ({
    resizable: true,
  }), []);

  // Selection column definition
  const selectionColumnDef = useMemo(() => ({
    width: 50,
    pinned: 'left' as const,
    lockPosition: true,
    suppressHeaderMenuButton: true,
  }), []);

  // Grid ready event handler
  const onGridReady = useCallback((params: GridReadyEvent) => {
    // Store the grid API reference
    setGridApi(params.api);

    // Apply the current theme
    const theme = getCurrentGridTheme();
    params.api.setGridOption('theme', theme);

    // Size columns to fit
    params.api.sizeColumnsToFit();
  }, []);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3 flex flex-row items-center justify-between">
          <CardTitle>Candidates ({candidates.length})</CardTitle>
          <div className="flex items-center gap-2">
            <ColumnVisibilityTrigger
              onClick={() => setShowColumnPanel(true)}
              variant="outline"
              size="sm"
            />
            {selectedRows.length > 0 && (
              <Button
                variant="destructive"
                size="sm"
                onClick={() => setShowDeleteDialog(true)}
                className="flex items-center gap-1"
              >
                <Trash2 className="h-4 w-4" />
                Delete Selected ({selectedRows.length})
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div
            className="ag-theme-quartz"
            style={{
              height: 'calc(100vh - 387px)',
              width: '100%',
            }}
          >
            <AgGridReact
              rowData={candidates}
              columnDefs={columnDefs}
              defaultColDef={defaultColDef}
              selectionColumnDef={selectionColumnDef}
              onGridReady={onGridReady}
              animateRows={true}
              rowSelection={{
                mode: 'multiRow',
                checkboxes: true,
                headerCheckbox: true
              }}
              suppressCellFocus={true}
              pagination={true}
              paginationPageSize={20}
              paginationPageSizeSelector={[10, 20, 50, 100]}
              domLayout="normal"
              theme={getCurrentGridTheme()}
              onSelectionChanged={onSelectionChanged}
              rowHeight={48}
              headerHeight={48}
              context={{
                onRemoveFromIncubator: handleRemoveFromIncubator,
                onNavigate: navigate
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={handleBulkDelete}
        itemCount={selectedRows.length}
        itemType="candidate"
        confirmationText="delete"
      />

      {/* Incubator Removal Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={showIncubatorRemovalDialog}
        onOpenChange={setShowIncubatorRemovalDialog}
        onConfirm={confirmRemoveFromIncubator}
        itemCount={1}
        itemType="candidate from Talent Incubator"
        confirmationText="remove"
      />

      {/* Column Visibility Panel */}
      <ColumnVisibilityPanel
        isOpen={showColumnPanel}
        onClose={() => setShowColumnPanel(false)}
        columns={columnDefs}
        gridApi={gridApi || undefined}
      />
    </div>
  );
}

// Main component with provider wrapper
export default function TableView(props: TableViewProps) {
  // Base column definitions for the provider
  const baseColumnDefs = useMemo<ColDef[]>(() => [
    {
      field: 'name',
      headerName: 'Name',
      cellRenderer: NameCellRenderer,
      flex: 2,
      minWidth: 200,
      sortable: true,
      filter: true,
      pinned: 'left',
      lockPosition: true
    },
    {
      field: 'email',
      headerName: 'Email',
      flex: 2,
      minWidth: 200,
      sortable: true,
      filter: true
    },
    {
      field: 'phone',
      headerName: 'Phone',
      flex: 1.5,
      minWidth: 130,
      sortable: true,
      filter: true
    },
    {
      field: 'position',
      headerName: 'Position',
      flex: 2,
      minWidth: 150,
      sortable: true,
      filter: true
    },
    {
      field: 'location',
      headerName: 'Location',
      flex: 1.5,
      minWidth: 120,
      sortable: true,
      filter: true
    },
    {
      field: 'experienceYears',
      headerName: 'Experience (Years)',
      flex: 1,
      minWidth: 100,
      sortable: true,
      filter: true
    },
    {
      field: 'currentCompany',
      headerName: 'Current Company',
      flex: 1.8,
      minWidth: 150,
      sortable: true,
      filter: true
    },
    {
      field: 'currentPosition',
      headerName: 'Current Position',
      flex: 1.8,
      minWidth: 150,
      sortable: true,
      filter: true
    },
    {
      field: 'education',
      headerName: 'Education',
      flex: 1.5,
      minWidth: 120,
      sortable: true,
      filter: true
    },
    {
      field: 'skills',
      headerName: 'Skills',
      cellRenderer: ArrayCellRenderer,
      flex: 2,
      minWidth: 150,
      sortable: true,
      filter: true
    },
    {
      field: 'resumeUrl',
      headerName: 'Resume URL',
      cellRenderer: UrlCellRenderer,
      flex: 1.5,
      minWidth: 120,
      sortable: true,
      filter: true
    },
    {
      field: 'linkedinUrl',
      headerName: 'LinkedIn URL',
      cellRenderer: UrlCellRenderer,
      flex: 1.5,
      minWidth: 120,
      sortable: true,
      filter: true
    },
    {
      field: 'githubUrl',
      headerName: 'GitHub URL',
      cellRenderer: UrlCellRenderer,
      flex: 1.5,
      minWidth: 120,
      sortable: true,
      filter: true
    },
    {
      field: 'portfolioUrl',
      headerName: 'Portfolio URL',
      cellRenderer: UrlCellRenderer,
      flex: 1.5,
      minWidth: 120,
      sortable: true,
      filter: true
    },
    {
      field: 'twitterUrl',
      headerName: 'Twitter URL',
      cellRenderer: UrlCellRenderer,
      flex: 1.5,
      minWidth: 120,
      sortable: true,
      filter: true
    },
    {
      field: 'desiredSalary',
      headerName: 'Desired Salary',
      flex: 1.2,
      minWidth: 110,
      sortable: true,
      filter: true
    },
    {
      field: 'salaryCurrency',
      headerName: 'Currency',
      flex: 0.8,
      minWidth: 80,
      sortable: true,
      filter: true
    },
    {
      field: 'availabilityDate',
      headerName: 'Availability Date',
      cellRenderer: DateCellRenderer,
      flex: 1.2,
      minWidth: 120,
      sortable: true,
      filter: true
    },
    {
      field: 'appliedDate',
      headerName: 'Applied Date',
      cellRenderer: DateCellRenderer,
      flex: 1,
      minWidth: 120,
      sortable: true,
      filter: true
    },
    {
      field: 'status',
      headerName: 'Primary Status',
      cellRenderer: StatusCellRenderer,
      flex: 1.2,
      minWidth: 140,
      sortable: true,
      filter: true
    },
    {
      field: 'secondaryStatus',
      headerName: 'Secondary Status',
      cellRenderer: SecondaryStatusCellRenderer,
      flex: 1.3,
      minWidth: 150,
      sortable: true,
      filter: true
    },
    {
      field: 'source',
      headerName: 'Source',
      flex: 1,
      minWidth: 100,
      sortable: true,
      filter: true
    },
    {
      field: 'stargetyId',
      headerName: 'Stargety ID',
      flex: 1,
      minWidth: 100,
      sortable: true,
      filter: true
    },
    {
      field: 'isDuplicate',
      headerName: 'Duplicate Status',
      flex: 1.2,
      minWidth: 110,
      sortable: true,
      filter: true
    },
    {
      field: 'englishLevel',
      headerName: 'English Level',
      flex: 1,
      minWidth: 100,
      sortable: true,
      filter: true
    },
    {
      field: 'interviewScore',
      headerName: 'Interview Score',
      flex: 1,
      minWidth: 110,
      sortable: true,
      filter: true
    },
    {
      field: 'challenge',
      headerName: 'Challenge',
      flex: 1.5,
      minWidth: 120,
      sortable: true,
      filter: true
    },
    {
      field: 'driveScore',
      headerName: 'Drive Score',
      flex: 1,
      minWidth: 100,
      sortable: true,
      filter: true
    },
    {
      field: 'resilienceScore',
      headerName: 'Resilience Score',
      flex: 1.2,
      minWidth: 110,
      sortable: true,
      filter: true
    },
    {
      field: 'collaborationScore',
      headerName: 'Collaboration Score',
      flex: 1.3,
      minWidth: 120,
      sortable: true,
      filter: true
    },
    {
      field: 'result',
      headerName: 'Result',
      flex: 1.5,
      minWidth: 120,
      sortable: true,
      filter: true
    },
    {
      field: 'isInIncubator',
      headerName: 'In Incubator',
      flex: 1,
      minWidth: 100,
      sortable: true,
      filter: true
    },
    {
      field: 'createdAt',
      headerName: 'Created Date',
      cellRenderer: DateCellRenderer,
      flex: 1.2,
      minWidth: 120,
      sortable: true,
      filter: true
    },
    {
      field: 'updatedAt',
      headerName: 'Updated Date',
      cellRenderer: DateCellRenderer,
      flex: 1.2,
      minWidth: 120,
      sortable: true,
      filter: true,
      hide: true // Hidden by default but available
    }
  ], []);

  return (
    <ColumnVisibilityProvider
      tableId="candidates-table"
      columns={baseColumnDefs}
    >
      <TableViewInternal {...props} />
    </ColumnVisibilityProvider>
  );
}