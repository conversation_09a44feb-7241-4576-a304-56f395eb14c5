import React, { useState, useRef, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Upload, FileText, CheckCircle, AlertCircle, Download } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { parseCSV } from '@/utils/csvUtils';
import { candidatesApi } from '@/services/apiService';
import DataPreviewTable from './DataPreviewTable';
import ColumnMappingBar from './ColumnMappingBar';
import ImportProgressDialog from './ImportProgressDialog';
import { transformCandidateData, mapCandidateStatus } from '@/utils/importValidators';
import { generateColumnSuggestions } from '@/utils/simplifiedCsvParser';

export interface CSVRecord {
  [key: string]: string;
}

export interface MappedColumn {
  csvColumn: string;
  targetField: string;
  confidence: number;
}

export interface ValidationError {
  row: number;
  field: string;
  value: string;
  message: string;
}

export interface ImportResult {
  success: boolean;
  imported: number;
  errors: ValidationError[];
  skipped: number;
}

interface SimplifiedCSVImportProps {
  onImportComplete?: (result: ImportResult) => void;
  onClose?: () => void;
}

// Email validation function - more flexible to handle various formats
const isValidEmail = (email: string): boolean => {
  console.log(`    🔍 Email validation input: "${email}" (type: ${typeof email})`);

  if (!email || typeof email !== 'string') {
    console.log(`    ❌ Email validation failed: not a string or empty`);
    return false;
  }

  // Clean the email - remove ALL types of whitespace and control characters
  const cleanEmail = email
    .replace(/[\r\n\t\f\v]/g, '') // Remove all whitespace control characters
    .trim(); // Remove leading/trailing spaces

  console.log(`    🧹 Cleaned email: "${cleanEmail}"`);

  // Skip validation if email is empty after cleaning
  if (!cleanEmail) {
    console.log(`    ✅ Email is empty after cleaning - allowing (optional field)`);
    return true; // Empty emails are allowed
  }

  // Check for obviously invalid patterns first
  if (cleanEmail.includes(' ')) {
    console.log(`    ❌ Email contains spaces`);
    return false; // No spaces allowed in emails
  }

  const atCount = cleanEmail.split('@').length - 1;
  if (atCount !== 1) {
    console.log(`    ❌ Email has ${atCount} @ symbols (must have exactly 1)`);
    return false; // Must have exactly one @
  }

  // More flexible email regex that handles various real-world formats
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  // Additional checks for common edge cases
  if (cleanEmail.includes('..')) {
    console.log(`    ❌ Email contains consecutive dots`);
    return false; // No consecutive dots
  }

  if (cleanEmail.startsWith('.') || cleanEmail.endsWith('.')) {
    console.log(`    ❌ Email starts or ends with dot`);
    return false; // No leading/trailing dots
  }

  if (cleanEmail.includes('@.') || cleanEmail.includes('.@')) {
    console.log(`    ❌ Email has dot adjacent to @`);
    return false; // No dots adjacent to @
  }

  const regexResult = emailRegex.test(cleanEmail);
  console.log(`    🎯 Regex test result: ${regexResult}`);

  return regexResult;
};

export default function SimplifiedCSVImport({ onImportComplete, onClose }: SimplifiedCSVImportProps) {
  const [csvData, setCsvData] = useState<CSVRecord[]>([]);
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [columnMappings, setColumnMappings] = useState<MappedColumn[]>([]);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [showProgress, setShowProgress] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Handle file upload
  const handleFileUpload = useCallback(async (file: File) => {
    console.log('📁 Starting file upload process...');
    console.log('📄 File details:', {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: new Date(file.lastModified).toISOString()
    });

    try {
      setCsvFile(file);
      console.log('🔄 Parsing CSV file...');
      const data = await parseCSV<CSVRecord>(file);
      console.log('✅ CSV parsed successfully. Records found:', data.length);
      console.log('📊 Sample data (first 3 rows):', data.slice(0, 3));

      if (data.length === 0) {
        console.log('❌ No data found in CSV file');
        toast({
          title: "Empty File",
          description: "The CSV file appears to be empty.",
          variant: "destructive",
        });
        return;
      }

      setCsvData(data);

      // Generate automatic column mappings
      const csvColumns = Object.keys(data[0]);
      console.log('📋 CSV columns detected:', csvColumns);

      const suggestions = generateColumnSuggestions(csvColumns).map(suggestion => ({
        csvColumn: suggestion.csvColumn,
        targetField: suggestion.suggestedField,
        confidence: suggestion.confidence
      }));

      console.log('🎯 Generated column suggestions:', suggestions);
      setColumnMappings(suggestions);

      toast({
        title: "File Loaded",
        description: `Successfully loaded ${data.length} records from ${file.name}`,
      });

      console.log('🎉 File upload process completed successfully');
    } catch (error) {
      console.log('❌ File upload failed:', error);
      toast({
        title: "Upload Error",
        description: `Failed to parse CSV file: ${(error as Error).message}`,
        variant: "destructive",
      });
    }
  }, [toast]);

  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback(() => {
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    const csvFile = files.find(file =>
      file.type === 'text/csv' || file.name.endsWith('.csv')
    );

    if (csvFile) {
      handleFileUpload(csvFile);
    } else {
      toast({
        title: "Invalid File",
        description: "Please upload a CSV file.",
        variant: "destructive",
      });
    }
  }, [handleFileUpload, toast]);

  // Handle file input change
  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  }, [handleFileUpload]);

  // Update column mapping
  const updateColumnMapping = useCallback((csvColumn: string, targetField: string) => {
    setColumnMappings(prev =>
      prev.map(mapping =>
        mapping.csvColumn === csvColumn
          ? { ...mapping, targetField, confidence: (targetField && targetField !== 'skip') ? 1 : 0 }
          : mapping
      )
    );
  }, []);

  // Validate and transform data
  const validateData = useCallback(() => {
    console.log('🔍 Starting data validation...');
    console.log('📊 CSV Data length:', csvData.length);
    console.log('🗺️ Column mappings:', columnMappings);

    const errors: ValidationError[] = [];
    const mappingMap = Object.fromEntries(
      columnMappings
        .filter(m => m.targetField && m.targetField !== 'skip')
        .map(m => [m.csvColumn, m.targetField])
    );

    console.log('🎯 Active mapping map:', mappingMap);

    csvData.forEach((row, index) => {
      console.log(`\n📝 Processing row ${index + 1}:`, row);

      // Transform row data
      const mappedData: any = {};
      Object.entries(row).forEach(([csvCol, value]) => {
        const targetField = mappingMap[csvCol];
        if (targetField) {
          mappedData[targetField] = value;
          console.log(`  ✅ Mapped: ${csvCol} -> ${targetField} = "${value}"`);
        } else {
          console.log(`  ⏭️ Skipped: ${csvCol} = "${value}"`);
        }
      });

      console.log(`  🔄 Mapped data:`, mappedData);

      // Apply status mapping and transformation
      const statusMapped = mapCandidateStatus(mappedData);
      console.log(`  📊 Status mapped:`, statusMapped);

      const transformed = transformCandidateData(statusMapped);
      console.log(`  🔧 Transformed:`, transformed);

      // Basic validation
      if (!transformed.first_name) {
        console.log(`  ❌ Row ${index + 1}: Missing first_name`);
        errors.push({
          row: index + 1,
          field: 'first_name',
          value: transformed.first_name || '',
          message: 'First name is required'
        });
      } else {
        console.log(`  ✅ Row ${index + 1}: first_name = "${transformed.first_name}"`);
      }

      // Email is now optional - only validate format if provided
      if (transformed.email && transformed.email.trim()) {
        console.log(`  📧 Row ${index + 1}: Validating email = "${transformed.email}"`);
        const emailValid = isValidEmail(transformed.email);
        console.log(`  📧 Row ${index + 1}: Email validation result = ${emailValid}`);

        if (!emailValid) {
          console.log(`  ❌ Row ${index + 1}: Invalid email format`);
          errors.push({
            row: index + 1,
            field: 'email',
            value: transformed.email || '',
            message: 'Invalid email format'
          });
        } else {
          console.log(`  ✅ Row ${index + 1}: Email is valid`);
        }
      } else {
        console.log(`  ⏭️ Row ${index + 1}: Email is empty or whitespace only - skipping validation`);
      }

      // Contact validation and dummy data filling
      const hasEmail = transformed.email && transformed.email.trim();
      const hasPhone = transformed.phone && transformed.phone.trim();

      console.log(`  📞 Row ${index + 1}: Contact check - Email: ${hasEmail ? 'Yes' : 'No'}, Phone: ${hasPhone ? 'Yes' : 'No'}`);

      if (!hasEmail && !hasPhone) {
        console.log(`  🔧 Row ${index + 1}: Missing contact information - filling with dummy data`);

        // Fill with dummy email to ensure successful import
        transformed.email = `dummy-contact-${index + 1}@placeholder.invalid`;
        console.log(`  🎭 Row ${index + 1}: Added dummy email: ${transformed.email}`);

        // Add note about dummy data
        const dummyNote = '[DUMMY DATA] This candidate was imported without contact information. Please update with real contact details.';
        if (transformed.interview_notes && transformed.interview_notes.trim()) {
          transformed.interview_notes = `${dummyNote}\n\nOriginal notes: ${transformed.interview_notes}`;
        } else {
          transformed.interview_notes = dummyNote;
        }
        console.log(`  📝 Row ${index + 1}: Added dummy data warning to notes`);
      } else {
        console.log(`  ✅ Row ${index + 1}: Has contact information`);
      }

      // Fill other empty fields with dummy data if needed
      if (!transformed.phone || !transformed.phone.trim()) {
        transformed.phone = `[DUMMY] ******-000-${String(index + 1).padStart(4, '0')}`;
        console.log(`  📱 Row ${index + 1}: Added dummy phone: ${transformed.phone}`);
      }

      if (!transformed.location || !transformed.location.trim()) {
        transformed.location = '[DUMMY] Location not provided';
        console.log(`  📍 Row ${index + 1}: Added dummy location`);
      }
    });

    console.log(`\n📋 Validation complete. Found ${errors.length} errors:`, errors);
    setValidationErrors(errors);
    return errors.length === 0;
  }, [csvData, columnMappings]);

  // Import data to database
  const handleImport = useCallback(async () => {
    console.log('🚀 Starting import process...');

    if (!validateData()) {
      console.log('❌ Validation failed, aborting import');
      toast({
        title: "Validation Failed",
        description: `Found ${validationErrors.length} validation errors. Please correct them before importing.`,
        variant: "destructive",
      });
      return;
    }

    console.log('✅ Validation passed, proceeding with import');
    setIsImporting(true);
    setShowProgress(true);
    setImportProgress(0);

    const mappingMap = Object.fromEntries(
      columnMappings
        .filter(m => m.targetField && m.targetField !== 'skip')
        .map(m => [m.csvColumn, m.targetField])
    );

    console.log('🎯 Import mapping map:', mappingMap);
    console.log(`📊 Importing ${csvData.length} rows...`);

    let imported = 0;
    let skipped = 0;
    const errors: ValidationError[] = [];

    try {
      for (let i = 0; i < csvData.length; i++) {
        const row = csvData[i];
        console.log(`\n🔄 Importing row ${i + 1}/${csvData.length}:`, row);

        try {
          // Transform row data
          const mappedData: any = {};
          Object.entries(row).forEach(([csvCol, value]) => {
            const targetField = mappingMap[csvCol];
            if (targetField) {
              mappedData[targetField] = value;
              console.log(`  📝 Import mapping: ${csvCol} -> ${targetField} = "${value}"`);
            }
          });

          console.log(`  🔄 Import mapped data:`, mappedData);

          // Apply status mapping and transformation
          const statusMapped = mapCandidateStatus(mappedData);
          console.log(`  📊 Import status mapped:`, statusMapped);

          const transformed = transformCandidateData(statusMapped);
          console.log(`  🔧 Import transformed:`, transformed);

          // Fill missing contact information with dummy data for successful import
          const hasEmail = transformed.email && transformed.email.trim();
          const hasPhone = transformed.phone && transformed.phone.trim();

          if (!hasEmail && !hasPhone) {
            console.log(`  🔧 Row ${i + 1}: Import - Adding dummy contact data`);
            transformed.email = `dummy-contact-${i + 1}@placeholder.invalid`;

            const dummyNote = '[DUMMY DATA] This candidate was imported without contact information. Please update with real contact details.';
            if (transformed.interview_notes && transformed.interview_notes.trim()) {
              transformed.interview_notes = `${dummyNote}\n\nOriginal notes: ${transformed.interview_notes}`;
            } else {
              transformed.interview_notes = dummyNote;
            }
          }

          // Fill other missing fields with dummy data
          if (!transformed.phone || !transformed.phone.trim()) {
            transformed.phone = `[DUMMY] ******-000-${String(i + 1).padStart(4, '0')}`;
          }

          if (!transformed.location || !transformed.location.trim()) {
            transformed.location = '[DUMMY] Location not provided';
          }

          console.log(`  🎭 Row ${i + 1}: Final data for import:`, transformed);

          // Create candidate via API
          console.log(`  🌐 Sending to API...`);
          const result = await candidatesApi.create(transformed);
          console.log(`  ✅ Row ${i + 1}: Successfully imported`, result);
          imported++;
        } catch (error: any) {
          console.log(`  ❌ Row ${i + 1}: Import failed:`, error);

          // Extract detailed error information
          let errorMessage = 'Unknown error';
          if (error?.response?.data?.message) {
            errorMessage = error.response.data.message;
          } else if (error?.response?.data?.error) {
            errorMessage = error.response.data.error;
          } else if (error?.message) {
            errorMessage = error.message;
          }

          console.log(`  🔍 Row ${i + 1}: Detailed error:`, {
            status: error?.response?.status,
            statusText: error?.response?.statusText,
            data: error?.response?.data,
            message: errorMessage
          });

          errors.push({
            row: i + 1,
            field: 'general',
            value: '',
            message: errorMessage
          });
          skipped++;
        }

        // Update progress
        const progress = ((i + 1) / csvData.length) * 100;
        setImportProgress(progress);
        console.log(`  📈 Progress: ${progress.toFixed(1)}%`);
      }

      const result: ImportResult = {
        success: errors.length === 0,
        imported,
        errors,
        skipped
      };

      console.log('\n🎯 Import Summary:');
      console.log(`  ✅ Successfully imported: ${imported}`);
      console.log(`  ⏭️ Skipped: ${skipped}`);
      console.log(`  ❌ Errors: ${errors.length}`);
      console.log('  📋 Error details:', errors);
      console.log('  🏁 Final result:', result);

      toast({
        title: "Import Complete",
        description: `Successfully imported ${imported} candidates. ${skipped} records were skipped.`,
        variant: imported > 0 ? "default" : "destructive",
      });

      onImportComplete?.(result);
    } catch (error) {
      console.log('💥 Import process crashed:', error);
      toast({
        title: "Import Failed",
        description: `Import process failed: ${(error as Error).message}`,
        variant: "destructive",
      });
    } finally {
      console.log('🔚 Import process finished');
      setIsImporting(false);
      setShowProgress(false);
    }
  }, [csvData, columnMappings, validationErrors.length, validateData, toast, onImportComplete]);

  // Reset state
  const handleReset = useCallback(() => {
    setCsvData([]);
    setCsvFile(null);
    setColumnMappings([]);
    setValidationErrors([]);
    setImportProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  const hasData = csvData.length > 0;
  const hasMappings = columnMappings.some(m => m.targetField && m.targetField !== 'skip');
  const hasErrors = validationErrors.length > 0;

  return (
    <div className="space-y-6">
      {/* File Upload Section */}
      {!hasData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Upload CSV File
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                isDragging
                  ? 'border-primary bg-primary/5'
                  : 'border-muted-foreground/25 hover:border-primary/50'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-lg font-medium mb-2">
                Drag and drop your CSV file here
              </p>
              <p className="text-sm text-muted-foreground mb-4">or</p>
              <Button onClick={() => fileInputRef.current?.click()}>
                Browse Files
              </Button>
              <input
                type="file"
                accept=".csv"
                ref={fileInputRef}
                onChange={handleFileChange}
                className="hidden"
              />
              <p className="text-xs text-muted-foreground mt-4">
                Supported format: CSV (Comma Separated Values)
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Data Preview and Mapping */}
      {hasData && (
        <>
          {/* File Info and Actions */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-green-600" />
                    <span className="font-medium">{csvFile?.name}</span>
                  </div>
                  <Badge variant="secondary">
                    {csvData.length} records
                  </Badge>
                  {hasMappings && (
                    <Badge variant="outline" className="text-green-600">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Mapped
                    </Badge>
                  )}
                  {hasErrors && (
                    <Badge variant="destructive">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {validationErrors.length} errors
                    </Badge>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={handleReset}>
                    Reset
                  </Button>
                  <Button
                    onClick={handleImport}
                    disabled={!hasMappings || hasErrors || isImporting}
                  >
                    Import {csvData.length} Records
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Column Mapping */}
          <ColumnMappingBar
            columnMappings={columnMappings}
            onMappingChange={updateColumnMapping}
          />

          {/* Validation Errors */}
          {hasErrors && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Found {validationErrors.length} validation errors. Please review and correct the data before importing.
              </AlertDescription>
            </Alert>
          )}

          {/* Data Preview */}
          <DataPreviewTable
            data={csvData}
            columnMappings={columnMappings}
            validationErrors={validationErrors}
            onDataChange={setCsvData}
          />
        </>
      )}

      {/* Import Progress Dialog */}
      <ImportProgressDialog
        isOpen={showProgress}
        progress={importProgress}
        onClose={() => setShowProgress(false)}
      />
    </div>
  );
}
