import { useState, useEffect, useCallback } from 'react';
import { candidatesApi } from '@/services/apiService';
import { useToast } from '@/components/ui/use-toast';

export interface Candidate {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  skills?: string[];
  experience_years?: number;
  status?: string;
  created_at: string;
  updated_at: string;
}

export function useCandidates() {
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  const fetchCandidates = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await candidatesApi.getAll();
      setCandidates(data);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch candidates'));
      toast({
        title: "Error",
        description: "Failed to fetch candidates. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchCandidates();
  }, [fetchCandidates]);

  const createCandidate = async (candidateData: Omit<Candidate, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const newCandidate = await candidatesApi.create(candidateData);
      setCandidates(prev => [...prev, newCandidate]);
      toast({
        title: "Success",
        description: "Candidate created successfully.",
      });
      return newCandidate;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create candidate';
      setError(err instanceof Error ? err : new Error(errorMessage));
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  const updateCandidate = async (id: string, candidateData: Partial<Candidate>) => {
    try {
      const updatedCandidate = await candidatesApi.update(id, candidateData);
      setCandidates(prev =>
        prev.map(candidate => candidate.id === id ? updatedCandidate : candidate)
      );
      toast({
        title: "Success",
        description: "Candidate updated successfully.",
      });
      return updatedCandidate;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update candidate';
      setError(err instanceof Error ? err : new Error(errorMessage));
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  const deleteCandidate = async (id: string) => {
    try {
      await candidatesApi.delete(id);
      setCandidates(prev => prev.filter(candidate => candidate.id !== id));
      toast({
        title: "Success",
        description: "Candidate deleted successfully.",
      });
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete candidate';
      setError(err instanceof Error ? err : new Error(errorMessage));
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  const bulkDeleteCandidates = async (ids: string[]) => {
    try {
      const result = await candidatesApi.bulkDelete(ids);
      setCandidates(prev => prev.filter(candidate => !ids.includes(candidate.id)));
      toast({
        title: "Success",
        description: `${result.deletedCount} candidates deleted successfully.`,
      });
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete candidates';
      setError(err instanceof Error ? err : new Error(errorMessage));
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  return {
    candidates,
    isLoading,
    error,
    refresh: fetchCandidates,
    createCandidate,
    updateCandidate,
    deleteCandidate,
    bulkDeleteCandidates
  };
}
