import { useState, useEffect, useCallback } from 'react';
import { jobApplicationsApi } from '@/services/apiService';
import { useToast } from '@/components/ui/use-toast';

export interface JobApplication {
  id: string;
  job_id: string;
  candidate_id: string;
  status?: string;
  stage?: string;
  applied_date?: string;
  cover_letter?: string;
  resume_url?: string;
  notes?: string;
  salary_expectation?: number;
  salary_currency?: string;
  source?: string;
  referrer?: string;
  created_at: string;
  updated_at: string;
  job?: {
    id: string;
    title: string;
  };
  candidate?: {
    id: string;
    first_name: string;
    last_name: string;
  };
}

export function useJobApplications() {
  const [jobApplications, setJobApplications] = useState<JobApplication[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  const fetchJobApplications = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await jobApplicationsApi.getAll();
      setJobApplications(data);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch job applications'));
      toast({
        title: "Error",
        description: "Failed to fetch job applications. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchJobApplications();
  }, [fetchJobApplications]);

  const createJobApplication = async (jobApplicationData: Omit<JobApplication, 'id' | 'created_at' | 'updated_at' | 'job' | 'candidate'>) => {
    try {
      const newJobApplication = await jobApplicationsApi.create(jobApplicationData);
      setJobApplications(prev => [...prev, newJobApplication]);
      toast({
        title: "Success",
        description: "Job application created successfully.",
      });
      return newJobApplication;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create job application';
      setError(err instanceof Error ? err : new Error(errorMessage));
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  const updateJobApplication = async (id: string, jobApplicationData: Partial<Omit<JobApplication, 'job' | 'candidate'>>) => {
    try {
      const updatedJobApplication = await jobApplicationsApi.update(id, jobApplicationData);
      setJobApplications(prev => 
        prev.map(jobApplication => jobApplication.id === id ? updatedJobApplication : jobApplication)
      );
      toast({
        title: "Success",
        description: "Job application updated successfully.",
      });
      return updatedJobApplication;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update job application';
      setError(err instanceof Error ? err : new Error(errorMessage));
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  const deleteJobApplication = async (id: string) => {
    try {
      await jobApplicationsApi.delete(id);
      setJobApplications(prev => prev.filter(jobApplication => jobApplication.id !== id));
      toast({
        title: "Success",
        description: "Job application deleted successfully.",
      });
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete job application';
      setError(err instanceof Error ? err : new Error(errorMessage));
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  return {
    jobApplications,
    isLoading,
    error,
    refresh: fetchJobApplications,
    createJobApplication,
    updateJobApplication,
    deleteJobApplication
  };
}

export function useJobApplication(id: string) {
  const [jobApplication, setJobApplication] = useState<JobApplication | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  const fetchJobApplication = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await jobApplicationsApi.getById(id);
      setJobApplication(data);
    } catch (err) {
      setError(err instanceof Error ? err : new Error(`Failed to fetch job application with ID ${id}`));
      toast({
        title: "Error",
        description: "Failed to fetch job application details. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [id, toast]);

  useEffect(() => {
    fetchJobApplication();
  }, [fetchJobApplication]);

  return {
    jobApplication,
    isLoading,
    error,
    refresh: fetchJobApplication
  };
}

export default useJobApplications;
