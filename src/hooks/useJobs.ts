import { useState, useEffect, useCallback } from 'react';
import { jobsApi } from '@/services/apiService';
import { useToast } from '@/components/ui/use-toast';

export interface Job {
  id: string;
  title: string;
  description?: string;
  requirements?: string;
  location?: string;
  salary_min?: number;
  salary_max?: number;
  status?: string;
  client_id?: string;
  client?: {
    id: string;
    company_name: string;
  };
  created_at: string;
  updated_at: string;
}

export function useJobs() {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  const fetchJobs = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await jobsApi.getAll();
      setJobs(data);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch jobs'));
      toast({
        title: "Error",
        description: "Failed to fetch jobs. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchJobs();
  }, [fetchJobs]);

  const createJob = async (jobData: Omit<Job, 'id' | 'created_at' | 'updated_at' | 'client'>) => {
    try {
      const newJob = await jobsApi.create(jobData);
      setJobs(prev => [...prev, newJob]);
      toast({
        title: "Success",
        description: "Job created successfully.",
      });
      return newJob;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create job';
      setError(err instanceof Error ? err : new Error(errorMessage));
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  const updateJob = async (id: string, jobData: Partial<Omit<Job, 'client'>>) => {
    try {
      const updatedJob = await jobsApi.update(id, jobData);
      setJobs(prev => 
        prev.map(job => job.id === id ? updatedJob : job)
      );
      toast({
        title: "Success",
        description: "Job updated successfully.",
      });
      return updatedJob;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update job';
      setError(err instanceof Error ? err : new Error(errorMessage));
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  const deleteJob = async (id: string) => {
    try {
      await jobsApi.delete(id);
      setJobs(prev => prev.filter(job => job.id !== id));
      toast({
        title: "Success",
        description: "Job deleted successfully.",
      });
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete job';
      setError(err instanceof Error ? err : new Error(errorMessage));
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw err;
    }
  };

  return {
    jobs,
    isLoading,
    error,
    refresh: fetchJobs,
    createJob,
    updateJob,
    deleteJob
  };
}
