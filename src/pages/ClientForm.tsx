import { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import DashboardLayout from '@/components/layout/DashboardLayout';
import ClientFormComponent from '@/components/clients/ClientForm';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useClient, useCreateClient, useUpdateClient } from '@/hooks/useClients';
import { useToast } from '@/components/ui/use-toast';
import useSaveData from '@/hooks/useSaveData';

export default function ClientFormPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const isEditMode = Boolean(id);

  // Obtener datos del cliente si estamos en modo edición
  const { client, isLoading: isLoadingClient } = useClient(id || '');

  // Usar el hook personalizado para guardar datos
  const { saveData, isLoading: isSaving } = useSaveData('clients', {
    onSuccess: () => {
      // Redirigir a la lista de clientes después de guardar
      navigate('/clients');
    },
    successMessage: isEditMode ? 'Client updated successfully' : 'Client created successfully',
  });

  // Mantener las mutaciones existentes para compatibilidad
  const createClientMutation = useCreateClient();
  const updateClientMutation = useUpdateClient();

  // Log para verificar el modo y los datos
  console.log('Client form:', { isEditMode, client, isLoadingClient });

  // Manejar el envío del formulario
  const handleSubmit = async (data: any) => {
    try {
      // Usar el nuevo servicio de guardado
      await saveData(data, isEditMode ? id : undefined);

      // También usar las mutaciones existentes para mantener la compatibilidad
      if (isEditMode && id) {
        // Actualizar cliente existente
        await updateClientMutation.mutateAsync({ id, ...data });
      } else {
        // Crear nuevo cliente
        await createClientMutation.mutateAsync(data);
      }
    } catch (error) {
      console.error('Error saving client:', error);
      // El toast ya se muestra en el hook useSaveData
    }
  };

  return (
    <DashboardLayout>
      <div className="flex items-center gap-2 mb-6">
        <Button variant="outline" size="icon" onClick={() => navigate(-1)}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold">
          {isEditMode ? 'Edit Client' : 'Add New Client'}
        </h1>
      </div>

      <ClientFormComponent
        client={client}
        onSubmit={handleSubmit}
        isLoading={isLoadingClient || isSaving || createClientMutation.isPending || updateClientMutation.isPending}
      />
    </DashboardLayout>
  );
}
