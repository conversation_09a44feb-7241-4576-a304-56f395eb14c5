import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import DashboardLayout from '@/components/layout/DashboardLayout';
import JobFormComponent from '@/components/jobs/JobForm';
import { Job } from '@/types/job';
import { Client } from '@/types/client';
import useSaveData from '@/hooks/useSaveData';

export default function JobForm() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [job, setJob] = useState<Job | undefined>(undefined);
  const [clients, setClients] = useState<Client[]>([]);
  const [isLoadingJob, setIsLoadingJob] = useState<boolean>(false);
  const [isLoadingClients, setIsLoadingClients] = useState<boolean>(false);

  const isEditMode = !!id;

  // Usar el hook personalizado para guardar datos
  const { saveData, isLoading: isSaving } = useSaveData('jobs', {
    onSuccess: () => {
      // Redirigir a la lista de trabajos después de guardar
      navigate('/jobs');
    },
    successMessage: isEditMode ? 'Job updated successfully' : 'Job created successfully',
  });

  // Simulación de carga de datos del trabajo para edición
  useEffect(() => {
    if (isEditMode) {
      setIsLoadingJob(true);
      // Aquí se cargarían los datos del trabajo desde la API
      // Por ahora, simulamos una carga con setTimeout
      setTimeout(() => {
        // Datos de ejemplo
        setJob({
          id: id!,
          title: 'Frontend Developer',
          description: 'We are looking for a skilled frontend developer...',
          status: 'open',
          type: 'full-time',
          location: 'Remote',
          salary_range: '$80,000 - $100,000',
          department: 'Engineering',
          requirements: ['3+ years of React experience', 'Strong TypeScript skills'],
          responsibilities: ['Develop new features', 'Maintain existing codebase'],
          posted_date: new Date().toISOString(),
          applicants_count: 0,
          client_id: 'client-1',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
        setIsLoadingJob(false);
      }, 500);
    }
  }, [id, isEditMode]);

  // Simulación de carga de clientes
  useEffect(() => {
    setIsLoadingClients(true);
    // Aquí se cargarían los clientes desde la API
    // Por ahora, simulamos una carga con setTimeout
    setTimeout(() => {
      // Datos de ejemplo
      setClients([
        {
          id: 'client-1',
          company_name: 'Acme Inc.',
          contact_name: 'John Doe',
          email: '<EMAIL>',
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 'client-2',
          company_name: 'Globex Corporation',
          contact_name: 'Jane Smith',
          email: '<EMAIL>',
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ]);
      setIsLoadingClients(false);
    }, 500);
  }, []);

  // Simulación de mutaciones para crear/actualizar trabajos
  const createJobMutation = {
    mutateAsync: async (data: any) => {
      // Simular una llamada a la API
      await new Promise(resolve => setTimeout(resolve, 1000));
      return { id: 'new-job-id', ...data };
    },
    isPending: false
  };

  const updateJobMutation = {
    mutateAsync: async (data: any) => {
      // Simular una llamada a la API
      await new Promise(resolve => setTimeout(resolve, 1000));
      return { id, ...data };
    },
    isPending: false
  };

  // Manejar el envío del formulario
  const handleSubmit = async (data: any) => {
    try {
      // Usar el nuevo servicio de guardado
      await saveData(data, isEditMode ? id : undefined);

      // También usar las mutaciones existentes para mantener la compatibilidad
      if (isEditMode && id) {
        // Actualizar trabajo existente
        await updateJobMutation.mutateAsync({ id, ...data });
      } else {
        // Crear nuevo trabajo
        await createJobMutation.mutateAsync(data);
      }
    } catch (error) {
      console.error('Error saving job:', error);
      // El toast ya se muestra en el hook useSaveData
    }
  };

  return (
    <DashboardLayout>
      <div className="flex items-center gap-2 mb-6">
        <Button variant="outline" size="icon" onClick={() => navigate(-1)}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold">
          {isEditMode ? 'Edit Job' : 'Add New Job'}
        </h1>
      </div>

      <JobFormComponent
        job={job}
        clients={clients}
        onSubmit={handleSubmit}
        isLoading={isLoadingJob || isLoadingClients || isSaving || createJobMutation.isPending || updateJobMutation.isPending}
      />
    </DashboardLayout>
  );
}
