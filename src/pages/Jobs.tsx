
import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Plus,
  Briefcase,
  Building,
  Users,
  CalendarDays
} from "lucide-react";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CSVUploadExport } from "@/components/common/CSVUploadExport";
import { parseCSV, exportToCSV } from "@/utils/csvUtils";
import { useToast } from "@/hooks/use-toast";
import { PageHeader } from "@/components/layout/PageHeader";

// Importar el hook de jobs
import { useJobs } from "@/hooks/useJobs";

interface Job {
  id: string;
  title: string;
  company: string;
  location: string;
  type: string;
  salary: string;
  posted: string;
  applicants: number;
  status: string;
}

export default function Jobs() {
  const [searchQuery, setSearchQuery] = useState("");
  const { jobs, isLoading, error, refresh: refreshJobs } = useJobs();
  const { toast } = useToast();

  // Filter jobs based on search query
  const filteredJobs = jobs.filter(job =>
    job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    job.company.toLowerCase().includes(searchQuery.toLowerCase()) ||
    job.location.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleJobUpload = async (file: File) => {
    try {
      const csvData = await parseCSV<any>(file);
      const newJobs = csvData.map((row: any) => ({
        id: row.id || crypto.randomUUID(),
        title: row.title || "",
        company: row.company || "",
        location: row.location || "",
        type: row.type || "Full-time",
        salary: row.salary || "",
        posted: row.posted || "today",
        applicants: parseInt(row.applicants || "0"),
        status: row.status || "active"
      }));

      // Here we would call the API to create the jobs
      // For now, just show a toast
      toast({
        title: "Import successful",
        description: `${newJobs.length} jobs imported successfully`
      });

      // Refresh the jobs list
      refreshJobs();
    } catch (error) {
      console.error("Error importing jobs:", error);
      toast({
        title: "Import failed",
        description: "Failed to import jobs. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleJobExport = () => {
    exportToCSV(jobs, "jobs.csv");

    toast({
      title: "Export successful",
      description: `${jobs.length} jobs exported successfully`
    });
  };

  return (
    <DashboardLayout>
      <PageHeader
        title="Job Openings"
        description="Manage your job listings and track applicants"
        searchPlaceholder="Search jobs..."
        searchValue={searchQuery}
        onSearchChange={setSearchQuery}
        primaryAction={{
          label: "Create Job",
          icon: <Plus className="h-4 w-4" />,
          href: "/jobs/new"
        }}
        onExport={handleJobExport}
        onImport={handleJobUpload}
        entityName="Jobs"
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8 mt-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Active Jobs</CardTitle>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{jobs.filter(job => job.status === "active").length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Partnered Companies</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{new Set(jobs.map(job => job.company)).size}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Total Applicants</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{jobs.reduce((acc, job) => acc + job.applicants, 0)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Average Time to Fill</CardTitle>
            <CalendarDays className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">18 days</div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <CardTitle>Job Listings</CardTitle>
            <Tabs defaultValue="all" className="w-auto">
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="active">Active</TabsTrigger>
                <TabsTrigger value="closed">Closed</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <div className="hidden md:grid grid-cols-8 gap-4 p-4 border-b bg-muted/50 text-sm font-medium">
              <div className="col-span-2">Position</div>
              <div className="col-span-2">Company</div>
              <div className="col-span-1">Type</div>
              <div className="col-span-1">Posted</div>
              <div className="col-span-1">Applicants</div>
              <div className="col-span-1 text-right">Actions</div>
            </div>
            <div className="divide-y">
              {filteredJobs.map(job => (
                <div key={job.id} className="grid grid-cols-1 md:grid-cols-8 gap-4 p-4 items-center">
                  <div className="col-span-2 font-medium">
                    <div>{job.title}</div>
                    <div className="text-sm text-muted-foreground md:hidden">{job.company}</div>
                  </div>
                  <div className="col-span-2 hidden md:block">{job.company}</div>
                  <div className="col-span-1 hidden md:block">{job.type}</div>
                  <div className="col-span-1 hidden md:block">{job.posted}</div>
                  <div className="col-span-1 hidden md:block">{job.applicants}</div>
                  <div className="col-span-1 flex justify-end">
                    <Button variant="outline" size="sm" asChild>
                      <a href={`/jobs/${job.id}`}>View</a>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {filteredJobs.length === 0 && (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <p className="text-sm text-muted-foreground mt-2">No job listings found matching your search criteria.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </DashboardLayout>
  );
}
