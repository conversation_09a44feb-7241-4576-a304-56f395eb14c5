import React, { useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { useNotifications } from '@/contexts/NotificationContext';
import { NotificationItem } from '@/components/notifications/NotificationItem';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ArchiveIcon,
  CheckSquare,
  Search,
  Trash2,
  X
} from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { ScrollArea } from '@/components/ui/scroll-area';

const Notifications = () => {
  const {
    notifications,
    markAsRead,
    bulkMarkAsRead,
    bulkArchive,
    bulkDelete
  } = useNotifications();

  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState('');

  // Filter notifications based on search query and active tab
  const filteredNotifications = notifications
    .filter(notification => {
      // Search filter
      const matchesSearch =
        notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        notification.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (notification.relatedTo?.name.toLowerCase().includes(searchQuery.toLowerCase()));

      // Tab filter
      if (activeTab === 'all') return !notification.archived && matchesSearch;
      if (activeTab === 'unread') return !notification.read && !notification.archived && matchesSearch;
      if (activeTab === 'archived') return notification.archived && matchesSearch;

      return matchesSearch;
    })
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

  // Toggle selection of a notification
  const toggleSelect = (id: string) => {
    if (selectedIds.includes(id)) {
      setSelectedIds(selectedIds.filter(selectedId => selectedId !== id));
    } else {
      setSelectedIds([...selectedIds, id]);
    }
  };

  // Select all visible notifications
  const selectAll = () => {
    if (selectedIds.length === filteredNotifications.length) {
      setSelectedIds([]);
    } else {
      setSelectedIds(filteredNotifications.map(n => n.id));
    }
  };

  // Handle bulk mark as read
  const handleBulkMarkAsRead = () => {
    bulkMarkAsRead(selectedIds);
    setSelectedIds([]);
  };

  // Handle bulk archive
  const handleBulkArchive = () => {
    bulkArchive(selectedIds);
    setSelectedIds([]);
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    setShowDeleteDialog(true);
  };

  // Confirm bulk delete
  const confirmBulkDelete = () => {
    if (deleteConfirmText === 'delete') {
      bulkDelete(selectedIds);
      setSelectedIds([]);
      setShowDeleteDialog(false);
      setDeleteConfirmText('');
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Notifications</h1>
            <p className="text-muted-foreground mt-1">Manage your notifications and stay updated</p>
          </div>

          <div className="relative w-full sm:w-80">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search notifications..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              aria-label="Search notifications"
            />
          </div>
        </div>

        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <TabsList className="w-full sm:w-auto">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="unread">Unread</TabsTrigger>
              <TabsTrigger value="archived">Archived</TabsTrigger>
            </TabsList>

            {selectedIds.length > 0 && (
              <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto bg-muted/50 p-2 rounded-lg">
                <span className="text-sm font-medium">
                  {selectedIds.length} selected
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBulkMarkAsRead}
                  disabled={activeTab === 'archived'}
                  className="h-8"
                >
                  <CheckSquare className="h-4 w-4 mr-1" />
                  Mark Read
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBulkArchive}
                  disabled={activeTab === 'archived'}
                  className="h-8"
                >
                  <ArchiveIcon className="h-4 w-4 mr-1" />
                  Archive
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleBulkDelete}
                  className="h-8"
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedIds([])}
                  className="h-8 ml-auto sm:ml-0"
                  aria-label="Clear selection"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>

          <TabsContent value="all" className="mt-6">
            <div className="bg-card rounded-md border shadow-sm">
              <div className="p-3 border-b flex items-center justify-between">
                <div
                  className="flex items-center gap-2 cursor-pointer"
                  onClick={selectAll}
                  role="checkbox"
                  aria-checked={selectedIds.length === filteredNotifications.length && filteredNotifications.length > 0}
                  tabIndex={0}
                  onKeyDown={(e) => e.key === 'Enter' && selectAll()}
                >
                  <div className={`w-4 h-4 rounded-sm border border-input flex items-center justify-center ${
                    selectedIds.length === filteredNotifications.length && filteredNotifications.length > 0
                      ? "bg-primary border-primary"
                      : ""
                  }`}>
                    {selectedIds.length === filteredNotifications.length && filteredNotifications.length > 0 && (
                      <CheckSquare className="h-3 w-3 text-primary-foreground" />
                    )}
                  </div>
                  <span className="text-sm font-medium">Select All</span>
                </div>

                <div className="text-sm text-muted-foreground">
                  {filteredNotifications.length} notification{filteredNotifications.length !== 1 ? 's' : ''}
                </div>
              </div>

              <ScrollArea className="h-[calc(100vh-320px)]">
                {filteredNotifications.length > 0 ? (
                  <div className="divide-y">
                    {filteredNotifications.map(notification => (
                      <NotificationItem
                        key={notification.id}
                        notification={notification}
                        onMarkAsRead={markAsRead}
                        showActions={false}
                        selected={selectedIds.includes(notification.id)}
                        onSelect={toggleSelect}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <Bell className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium">No notifications found</h3>
                    <p className="text-sm text-muted-foreground mt-2 max-w-md">
                      {searchQuery
                        ? 'Try adjusting your search to find what you\'re looking for.'
                        : activeTab === 'unread'
                          ? 'You have no unread notifications.'
                          : activeTab === 'archived'
                            ? 'You have no archived notifications.'
                            : 'You have no notifications at this time.'}
                    </p>
                  </div>
                )}
              </ScrollArea>
            </div>
          </TabsContent>

          <TabsContent value="unread" className="mt-6">
            <div className="bg-card rounded-md border shadow-sm">
              <div className="p-3 border-b flex items-center justify-between">
                <div
                  className="flex items-center gap-2 cursor-pointer"
                  onClick={selectAll}
                  role="checkbox"
                  aria-checked={selectedIds.length === filteredNotifications.length && filteredNotifications.length > 0}
                  tabIndex={0}
                  onKeyDown={(e) => e.key === 'Enter' && selectAll()}
                >
                  <div className={`w-4 h-4 rounded-sm border border-input flex items-center justify-center ${
                    selectedIds.length === filteredNotifications.length && filteredNotifications.length > 0
                      ? "bg-primary border-primary"
                      : ""
                  }`}>
                    {selectedIds.length === filteredNotifications.length && filteredNotifications.length > 0 && (
                      <CheckSquare className="h-3 w-3 text-primary-foreground" />
                    )}
                  </div>
                  <span className="text-sm font-medium">Select All</span>
                </div>

                <div className="text-sm text-muted-foreground">
                  {filteredNotifications.length} unread notification{filteredNotifications.length !== 1 ? 's' : ''}
                </div>
              </div>

              <ScrollArea className="h-[calc(100vh-320px)]">
                {filteredNotifications.length > 0 ? (
                  <div className="divide-y">
                    {filteredNotifications.map(notification => (
                      <NotificationItem
                        key={notification.id}
                        notification={notification}
                        onMarkAsRead={markAsRead}
                        showActions={false}
                        selected={selectedIds.includes(notification.id)}
                        onSelect={toggleSelect}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <Bell className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium">No unread notifications</h3>
                    <p className="text-sm text-muted-foreground mt-2 max-w-md">
                      {searchQuery
                        ? 'Try adjusting your search to find what you\'re looking for.'
                        : 'All your notifications have been read.'}
                    </p>
                  </div>
                )}
              </ScrollArea>
            </div>
          </TabsContent>

          <TabsContent value="archived" className="mt-6">
            <div className="bg-card rounded-md border shadow-sm">
              <div className="p-3 border-b flex items-center justify-between">
                <div
                  className="flex items-center gap-2 cursor-pointer"
                  onClick={selectAll}
                  role="checkbox"
                  aria-checked={selectedIds.length === filteredNotifications.length && filteredNotifications.length > 0}
                  tabIndex={0}
                  onKeyDown={(e) => e.key === 'Enter' && selectAll()}
                >
                  <div className={`w-4 h-4 rounded-sm border border-input flex items-center justify-center ${
                    selectedIds.length === filteredNotifications.length && filteredNotifications.length > 0
                      ? "bg-primary border-primary"
                      : ""
                  }`}>
                    {selectedIds.length === filteredNotifications.length && filteredNotifications.length > 0 && (
                      <CheckSquare className="h-3 w-3 text-primary-foreground" />
                    )}
                  </div>
                  <span className="text-sm font-medium">Select All</span>
                </div>

                <div className="text-sm text-muted-foreground">
                  {filteredNotifications.length} archived notification{filteredNotifications.length !== 1 ? 's' : ''}
                </div>
              </div>

              <ScrollArea className="h-[calc(100vh-320px)]">
                {filteredNotifications.length > 0 ? (
                  <div className="divide-y">
                    {filteredNotifications.map(notification => (
                      <NotificationItem
                        key={notification.id}
                        notification={notification}
                        onMarkAsRead={markAsRead}
                        showActions={false}
                        selected={selectedIds.includes(notification.id)}
                        onSelect={toggleSelect}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <ArchiveIcon className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium">No archived notifications</h3>
                    <p className="text-sm text-muted-foreground mt-2 max-w-md">
                      {searchQuery
                        ? 'Try adjusting your search to find what you\'re looking for.'
                        : 'You haven\'t archived any notifications yet.'}
                    </p>
                  </div>
                )}
              </ScrollArea>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete {selectedIds.length} selected notification{selectedIds.length !== 1 ? 's' : ''}.
              <div className="mt-4">
                <p className="text-sm font-medium mb-2">Type "delete" to confirm:</p>
                <Input
                  value={deleteConfirmText}
                  onChange={(e) => setDeleteConfirmText(e.target.value)}
                  placeholder="delete"
                  className="w-full"
                  autoFocus
                  aria-label="Type 'delete' to confirm"
                />
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmBulkDelete}
              disabled={deleteConfirmText !== 'delete'}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Forever
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </DashboardLayout>
  );
};

export default Notifications;
