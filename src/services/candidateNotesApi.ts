/**
 * API service for candidate notes
 */

import { apiClient } from './apiService';
import { CandidateNote, CreateCandidateNoteRequest, UpdateCandidateNoteRequest } from '@/types/candidateNote';

/**
 * Candidate Notes API service
 */
export const candidateNotesApi = {
  /**
   * Get all notes for a specific candidate
   */
  getByCandidateId: async (candidateId: string): Promise<CandidateNote[]> => {
    console.log('🌐 candidateNotesApi: Fetching notes for candidate:', candidateId);
    try {
      const url = `/candidate-notes/candidate/${candidateId}`;
      console.log('🌐 candidateNotesApi: Making GET request to:', url);
      const response = await apiClient.get(url);
      console.log('🌐 candidateNotesApi: Response received:', response);
      console.log('🌐 candidateNotesApi: Response data:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ candidateNotesApi: Error fetching candidate notes:', error);
      console.error('❌ candidateNotesApi: Error response:', error.response?.data);
      console.error('❌ candidateNotesApi: Error status:', error.response?.status);
      throw error;
    }
  },

  /**
   * Create a new note for a candidate
   */
  create: async (data: CreateCandidateNoteRequest & { user_id: string }): Promise<CandidateNote> => {
    console.log('🌐 candidateNotesApi: Creating note with data:', data);
    try {
      const url = '/candidate-notes';
      console.log('🌐 candidateNotesApi: Making POST request to:', url);
      const response = await apiClient.post(url, data);
      console.log('🌐 candidateNotesApi: Create response received:', response);
      console.log('🌐 candidateNotesApi: Created note data:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ candidateNotesApi: Error creating candidate note:', error);
      console.error('❌ candidateNotesApi: Error response:', error.response?.data);
      console.error('❌ candidateNotesApi: Error status:', error.response?.status);
      throw error;
    }
  },

  /**
   * Update an existing note
   */
  update: async (id: string, data: UpdateCandidateNoteRequest & { user_id: string }): Promise<CandidateNote> => {
    try {
      const response = await apiClient.put(`/candidate-notes/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating candidate note:', error);
      throw error;
    }
  },

  /**
   * Delete a note
   */
  delete: async (id: string, userId: string): Promise<void> => {
    try {
      await apiClient.delete(`/candidate-notes/${id}`, {
        data: { user_id: userId }
      });
    } catch (error) {
      console.error('Error deleting candidate note:', error);
      throw error;
    }
  }
};
