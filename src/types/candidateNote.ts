/**
 * TypeScript interfaces for candidate notes system
 */

/**
 * Interface for a candidate note
 */
export interface CandidateNote {
  id: string;
  candidate_id: string;
  user_id: string;
  content: string;
  created_at: string;
  updated_at: string;
  // Populated fields from joins
  user_name?: string;
  user_email?: string;
  user_avatar?: string;
}

/**
 * Interface for creating a new candidate note
 */
export interface CreateCandidateNoteRequest {
  candidate_id: string;
  content: string;
}

/**
 * Interface for updating an existing candidate note
 */
export interface UpdateCandidateNoteRequest {
  content: string;
}

/**
 * Interface for candidate note API responses
 */
export interface CandidateNoteResponse {
  success: boolean;
  data?: CandidateNote | CandidateNote[];
  error?: string;
  message?: string;
}

/**
 * Interface for candidate notes with user information for display
 */
export interface CandidateNoteWithUser extends CandidateNote {
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
}
