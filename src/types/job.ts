export type JobStatus = 'open' | 'closed' | 'draft' | 'archived';

export type JobType = 'full-time' | 'part-time' | 'contract' | 'internship' | 'remote';

export interface Job {
  id: string;
  title: string;
  description: string;
  status: JobStatus;
  type: JobType;
  location: string;
  salary_range?: string;
  department?: string;
  requirements?: string[];
  responsibilities?: string[];
  posted_date: string;
  closing_date?: string;
  applicants_count: number;
  client_id?: string; // Referencia al cliente asociado con este trabajo
  company?: string; // Para compatibilidad con el código existente
  created_at: string;
  updated_at: string;
}

// Tipo para crear un nuevo trabajo
export interface CreateJobInput {
  title: string;
  description: string;
  status: JobStatus;
  type: JobType;
  location: string;
  salary_range?: string;
  department?: string;
  requirements?: string[];
  responsibilities?: string[];
  closing_date?: string;
  client_id?: string;
}
